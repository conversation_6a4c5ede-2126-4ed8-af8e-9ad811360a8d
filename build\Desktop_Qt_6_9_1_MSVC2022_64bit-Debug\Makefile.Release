#############################################################################
# Makefile for building: ChatServer
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  ..\..\ChatServer.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = cl
CXX           = cl
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_DEPRECATED_WARNINGS -DNDEBUG -DQT_QML_DEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_SQL_LIB -DQT_CORE_LIB
CFLAGS        = -nologo -Zc:wchar_t -FS -Zc:strictStrings -O2 -MD -utf-8 -W3 -w44456 -w44457 -w44458 $(DEFINES)
CXXFLAGS      = -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44456 -w44457 -w44458 -wd4577 -wd4467 -EHsc $(DEFINES)
INCPATH       = -I..\..\..\ChatServer -I. -IE:\YingYong\QT\6.9.1\msvc2022_64\include -IE:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets -IE:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui -IE:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork -IE:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql -IE:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore -Irelease -I. -I/include -IE:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc 
LINKER        = link
LFLAGS        = /NOLOGO /DYNAMICBASE /NXCOMPAT /OPT:REF /OPT:ICF /INCREMENTAL:NO /SUBSYSTEM:WINDOWS "/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"
LIBS          = E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Gui.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Network.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Sql.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Core.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6EntryPoint.lib shell32.lib  
QMAKE         = E:\YingYong\QT\6.9.1\msvc2022_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = E:\YingYong\QT\6.9.1\msvc2022_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = E:\YingYong\QT\6.9.1\msvc2022_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = release

####### Files

SOURCES       = ..\..\main.cpp \
		..\..\server.cpp \
		..\..\userlist.cpp release\moc_server.cpp
OBJECTS       = release\main.obj \
		release\server.obj \
		release\userlist.obj \
		release\moc_server.obj

DIST          =  ..\..\server.h \
		..\..\userlist.h ..\..\main.cpp \
		..\..\server.cpp \
		..\..\userlist.cpp
QMAKE_TARGET  = ChatServer
DESTDIR        = release\ #avoid trailing-slash linebreak
TARGET         = ChatServer.exe
DESTDIR_TARGET = release\ChatServer.exe

####### Implicit rules

.SUFFIXES: .c .cpp .cc .cxx

{.}.cpp{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{.}.cc{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{.}.cxx{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{.}.c{release\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{release}.cpp{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{release}.cc{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{release}.cxx{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{release}.c{release\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{..\..}.cpp{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{..\..}.cc{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{..\..}.cxx{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{..\..}.c{release\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

####### Build rules

first: all
all: Makefile.Release  release\ChatServer.exe

release\ChatServer.exe: E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Gui.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Network.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Sql.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Core.lib E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6EntryPoint.lib ui_server.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) /MANIFEST:embed /OUT:$(DESTDIR_TARGET) @<<
release\main.obj release\server.obj release\userlist.obj release\moc_server.obj
$(LIBS)
<<

qmake: FORCE
	@$(QMAKE) -o Makefile.Release ..\..\ChatServer.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) ChatServer.zip $(SOURCES) $(DIST) ..\..\ChatServer.pro E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\spec_pre.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\windows-desktop.conf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\windows_vulkan_sdk.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\windows-vulkan.conf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\msvc-desktop.conf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\qconfig.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_freetype.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_libjpeg.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_libpng.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_openxr_loader.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_charts.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_charts_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_chartsqml.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_chartsqml_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_concurrent.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_concurrent_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_core.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_core_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_dbus.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_dbus_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designer.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designer_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designercomponents_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_entrypoint_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_example_icons_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_fb_support_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_freetype_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_gui.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_gui_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_harfbuzz_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_help.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_help_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_httpserver.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_httpserver_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_jpeg_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsanimation.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsanimation_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsplatform.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsplatform_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssettings.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssettings_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_linguist.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimedia.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimedia_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediaquick_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_network.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_network_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_opengl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_opengl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_packetprotocol_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_png_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_printsupport.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_printsupport_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatch_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qml.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qml_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcore.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcore_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmldebug_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmldom_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlformat_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlls_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltest.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltest_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3d.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3d_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickeffects.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickeffects_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickparticles_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickshapes_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_serialport.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_serialport_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_shadertools.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_shadertools_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_sql.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_sql_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svg.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svg_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testinternals_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testlib.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testlib_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_tools_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uiplugin.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uitools.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uitools_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_websockets.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_websockets_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_widgets.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_widgets_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_xml.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_xml_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_zlib_private.pri E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qt_functions.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qt_config.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc\qmake.conf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\spec_post.prf .qmake.stash E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\exclusive_builds.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\msvc-version.conf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\toolchain.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\default_pre.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\default_pre.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\resolve_config.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\exclusive_builds_post.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\default_post.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\build_pass.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qml_debug.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\precompile_header.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\warn_on.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\permissions.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qt.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\resources_functions.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\resources.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\moc.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\opengl.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\uic.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qmake_use.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\file_copies.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\windows.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\testcase_targets.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\exceptions.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\yacc.prf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\lex.prf ..\..\ChatServer.pro E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.prl E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Gui.prl E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Network.prl E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Sql.prl E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Core.prl E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6EntryPoint.prl    E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\data\dummy.cpp ..\..\server.h ..\..\userlist.h  ..\..\main.cpp ..\..\server.cpp ..\..\userlist.cpp ..\..\server.ui    

clean: compiler_clean 
	-$(DEL_FILE) release\main.obj release\server.obj release\userlist.obj release\moc_server.obj

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: release\moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) release\moc_predefs.h
release\moc_predefs.h: E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\data\dummy.cpp
	cl -BxE:\YingYong\QT\6.9.1\msvc2022_64\bin\qmake.exe -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\data\dummy.cpp 2>NUL >release\moc_predefs.h

compiler_moc_header_make_all: release\moc_server.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) release\moc_server.cpp
release\moc_server.cpp: ..\..\server.h \
		..\..\userlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlDatabase \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqldatabase.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsqlglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsql-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsqlexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetaobject.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlQuery \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqlquery.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QVariant \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlQueryModel \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqlquerymodel.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qabstractitemmodel.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\QWidget \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qwidget.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qaction.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qkeysequence.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qicon.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpalette.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qcursor.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qbitmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qevent.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qeventpoint.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qvector2d.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qvectornd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpointingdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qinputdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qscreen.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QRect \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QSize \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QSizeF \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\QTransform \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnativeinterface.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qscreen_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfuture.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfutureinterface.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmutex.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtsan_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qresultstore.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfuture_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qthreadpool.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qthread.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrunnable.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qexception.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpromise.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qinputmethod.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\QTcpServer \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpserver.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qhostaddress.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\QTcpSocket \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		release\moc_predefs.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\bin\moc.exe
	E:\YingYong\QT\6.9.1\msvc2022_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include D:/Desktop_xiao/OnlineChat-master/ChatServer/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/release/moc_predefs.h -IE:/YingYong/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc -ID:/Desktop_xiao/OnlineChat-master/ChatServer -IE:/YingYong/QT/6.9.1/msvc2022_64/include -IE:/YingYong/QT/6.9.1/msvc2022_64/include/QtWidgets -IE:/YingYong/QT/6.9.1/msvc2022_64/include/QtGui -IE:/YingYong/QT/6.9.1/msvc2022_64/include/QtNetwork -IE:/YingYong/QT/6.9.1/msvc2022_64/include/QtSql -IE:/YingYong/QT/6.9.1/msvc2022_64/include/QtCore -I. -IE:\YingYong\VisualStudio\Community\VC\Tools\MSVC\14.44.35207\include -IE:\YingYong\VisualStudio\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include -IE:\YingYong\VisualStudio\Community\VC\Auxiliary\VS\include -I"D:\Windows Kits\10\include\10.0.26100.0\ucrt" -I"D:\Windows Kits\10\\include\10.0.26100.0\\um" -I"D:\Windows Kits\10\\include\10.0.26100.0\\shared" -I"D:\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"D:\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" ..\..\server.h -o release\moc_server.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_server.h
compiler_uic_clean:
	-$(DEL_FILE) ui_server.h
ui_server.h: ..\..\server.ui \
		E:\YingYong\QT\6.9.1\msvc2022_64\bin\uic.exe
	E:\YingYong\QT\6.9.1\msvc2022_64\bin\uic.exe ..\..\server.ui -o ui_server.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

release\main.obj: ..\..\main.cpp ..\..\server.h \
		..\..\userlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlDatabase \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqldatabase.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsqlglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsql-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsqlexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetaobject.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlQuery \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqlquery.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QVariant \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlQueryModel \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqlquerymodel.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qabstractitemmodel.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\QWidget \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qwidget.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qaction.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qkeysequence.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qicon.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpalette.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qcursor.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qbitmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qevent.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qeventpoint.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qvector2d.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qvectornd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpointingdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qinputdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qscreen.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QRect \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QSize \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QSizeF \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\QTransform \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnativeinterface.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qscreen_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfuture.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfutureinterface.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmutex.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtsan_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qresultstore.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfuture_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qthreadpool.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qthread.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrunnable.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qexception.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpromise.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qinputmethod.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\QTcpServer \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpserver.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qhostaddress.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\QTcpSocket \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\QApplication \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qapplication.h

release\server.obj: ..\..\server.cpp ..\..\server.h \
		..\..\userlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlDatabase \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqldatabase.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsqlglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsql-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsqlexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetaobject.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlQuery \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqlquery.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QVariant \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlQueryModel \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqlquerymodel.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qabstractitemmodel.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\QWidget \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qwidget.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qaction.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qkeysequence.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qicon.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpalette.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qcursor.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qbitmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qevent.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qeventpoint.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qvector2d.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qvectornd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpointingdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qinputdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qscreen.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QRect \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QSize \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QSizeF \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\QTransform \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnativeinterface.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qscreen_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfuture.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfutureinterface.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmutex.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtsan_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qresultstore.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfuture_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qthreadpool.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qthread.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrunnable.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qexception.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpromise.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qinputmethod.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\QTcpServer \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpserver.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qhostaddress.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\QTcpSocket \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		ui_server.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QDebug

release\userlist.obj: ..\..\userlist.cpp ..\..\userlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlDatabase \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqldatabase.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsqlglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsql-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qtsqlexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetaobject.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlQuery \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqlquery.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QVariant \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlQueryModel \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqlquerymodel.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qabstractitemmodel.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\QMessageBox \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qmessagebox.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qdialog.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qwidget.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qaction.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qkeysequence.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qicon.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpalette.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qcursor.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qbitmap.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qevent.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qeventpoint.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qvector2d.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qvectornd.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qpointingdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qinputdevice.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qscreen.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QRect \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QSize \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QSizeF \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\QTransform \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qnativeinterface.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qscreen_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfuture.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfutureinterface.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qmutex.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qtsan_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qresultstore.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qfuture_impl.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qthreadpool.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qthread.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qrunnable.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qexception.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qpromise.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qinputmethod.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qdialogbuttonbox.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\QMainWindow \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qmainwindow.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtabwidget.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\QTableView \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtableview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractitemview.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractscrollarea.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qframe.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qitemselectionmodel.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractitemdelegate.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qstyleoption.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractspinbox.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtGui\qvalidator.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qregularexpression.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qslider.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractslider.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qstyle.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qtabbar.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtWidgets\qrubberband.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\QSqlError \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtSql\qsqlerror.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QDebug \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\QTime \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qdatetime.h \
		E:\YingYong\QT\6.9.1\msvc2022_64\include\QtCore\qcalendar.h

release\moc_server.obj: release\moc_server.cpp 

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

